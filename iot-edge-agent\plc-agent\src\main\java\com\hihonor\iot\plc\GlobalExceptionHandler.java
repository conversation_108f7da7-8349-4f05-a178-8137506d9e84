/*
 * Copyright (c) 2025, Honor Device Co., Ltd. All rights reserved.
 */

package com.hihonor.iot.plc;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.core.annotation.Order;
import org.springframework.core.Ordered;
import com.fasterxml.jackson.core.JsonParseException;



/**
 * 全局异常处理器
 * 统一处理Controller层抛出的异常，返回标准化的ApiResponse响应
 * 
 * <AUTHOR>
 * @since 2025-06-11
 */
@RestControllerAdvice
@Order(Ordered.HIGHEST_PRECEDENCE)
public class GlobalExceptionHandler {

    private static final Logger log = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 处理参数校验异常 - @Valid注解校验失败
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ApiResponse<String> handleValidationException(MethodArgumentNotValidException ex) {
        String errorMessage = ex.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining("; "));

        log.warn("参数校验失败: {}", errorMessage);

        return new ApiResponse<>(
                false,
                "参数校验失败",
                null);
    }

    /**
     * 处理参数绑定异常
     */
    @ExceptionHandler(BindException.class)
    public ApiResponse<String> handleBindException(BindException ex) {
        String errorMessage = ex.getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining("; "));

        log.warn("参数绑定失败: {}", errorMessage);

        return new ApiResponse<>(
                false,
                "参数绑定失败",
                null);
    }

    /**
     * 处理约束违反异常 - @Validated校验失败
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ApiResponse<String> handleConstraintViolationException(ConstraintViolationException ex) {
        String errorMessage = ex.getConstraintViolations().stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining("; "));

        log.warn("约束校验失败: {}", errorMessage);

        return new ApiResponse<>(
                false,
                "约束校验失败",
                null);
    }

    /**
     * 处理参数类型不匹配异常
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ApiResponse<String> handleTypeMismatchException(MethodArgumentTypeMismatchException ex) {
        log.warn("参数类型不匹配: 参数 '{}' 的值 '{}' 类型不匹配", ex.getName(), ex.getValue());

        return new ApiResponse<>(
                false,
                "参数类型不匹配",
                null);
    }

    /**
     * 处理缺少请求参数异常
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public ApiResponse<String> handleMissingParameterException(
            MissingServletRequestParameterException ex) {
        log.warn("缺少请求参数: {}", ex.getParameterName());

        return new ApiResponse<>(
                false,
                "缺少必需的请求参数",
                null);
    }

    /**
     * 处理JSON解析异常 - 请求体格式错误
     */
    @ExceptionHandler(JsonParseException.class)
    public ApiResponse<String> handleJsonParseException(JsonParseException ex) {
        log.warn("JSON解析失败: {}", ex.getMessage());

        return new ApiResponse<>(
                false,
                "请求格式错误",
                null);
    }

    /**
     * 处理HTTP消息不可读异常 - 包含JSON解析错误
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ApiResponse<String> handleHttpMessageNotReadableException(HttpMessageNotReadableException ex) {
        log.warn("HTTP消息解析失败: {}", ex.getMessage());

        // 检查是否是JSON解析异常
        if (ex.getCause() instanceof JsonParseException) {
            return new ApiResponse<>(
                    false,
                    "请求格式错误",
                    null);
        }

        return new ApiResponse<>(
                false,
                "请求数据格式错误",
                null);
    }

    /**
     * 处理设备通信超时异常
     */
    @ExceptionHandler(TimeoutException.class)
    public ApiResponse<String> handleTimeoutException(TimeoutException ex) {
        log.warn("设备通信超时: {}", ex.getMessage());

        return new ApiResponse<>(
                false,
                "设备响应超时，请稍后重试",
                null);
    }

    /**
     * 处理执行异常
     */
    @ExceptionHandler(ExecutionException.class)
    public ApiResponse<String> handleExecutionException(ExecutionException ex) {
        log.error("设备命令执行异常", ex);

        return new ApiResponse<>(
                false,
                "设备命令执行失败",
                null);
    }

    /**
     * 处理中断异常
     */
    @ExceptionHandler(InterruptedException.class)
    public ApiResponse<String> handleInterruptedException(InterruptedException ex) {
        Thread.currentThread().interrupt(); // 恢复中断状态
        log.warn("请求中断", ex);

        return new ApiResponse<>(
                false,
                "请求被中断",
                null);
    }

    /**
     * 处理IllegalArgumentException
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ApiResponse<String> handleIllegalArgumentException(IllegalArgumentException ex) {
        log.warn("参数错误", ex);

        return new ApiResponse<>(
                false,
                "参数错误",
                null);
    }

    /**
     * 处理所有其他未捕获的异常
     */
    @ExceptionHandler(Exception.class)
    public ApiResponse<String> handleGenericException(Exception ex) {
        log.error("系统内部错误", ex);

        return new ApiResponse<>(
                false,
                "系统内部错误，请稍后重试",
                null);
    }
}