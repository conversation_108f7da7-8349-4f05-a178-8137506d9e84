/*
 * Copyright (c) 2025, Honor Device Co., Ltd. All rights reserved.
 */

package com.hihonor.iot.plc.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.FilterType;

/**
 * 异常处理器配置类
 * 确保我们的全局异常处理器优先级最高
 * 
 * <AUTHOR>
 * @since 2025-06-25
 */
@Configuration
@ComponentScan(
    basePackages = "com.hihonor.iot.plc",
    excludeFilters = {
        @ComponentScan.Filter(
            type = FilterType.REGEX,
            pattern = "com\\.hihonor\\.iot\\.edge\\.common\\.handler\\..*"
        )
    }
)
public class ExceptionHandlerConfig {
    // 配置类用于控制组件扫描，确保我们的异常处理器优先
} 