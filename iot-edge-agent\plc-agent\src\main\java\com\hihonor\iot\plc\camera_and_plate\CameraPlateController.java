/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.camera_and_plate;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hihonor.iot.plc.ApiResponse;
import com.hihonor.iot.plc.camera_and_plate.request.DeleteCameraPlateRequest;
import com.hihonor.iot.plc.camera_and_plate.request.SearchCriteria;
import com.hihonor.iot.plc.camera_and_plate.request.UpsertCameraPlateRequest;

import lombok.Data;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-05-10
 */
@RestController
@Validated
@RequestMapping("/plc/camera-plate")
public class CameraPlateController {

    @Autowired
    private CameraPlateService cameraPlateService;

    @PostMapping("/upsert-camera-plate")
    public ApiResponse<Map<String, Boolean>> upsertCameraPlateAssociations(@Valid @RequestBody UpsertCameraPlateRequest request) {
        Map<String, Boolean> results = cameraPlateService.upsertCameraPlateAssociations(request.getAssociations());
        ApiResponse<Map<String, Boolean>> response = new ApiResponse<>(true, "操作成功", results);
        response.setLength(results.size());
        return response;
    }

    @PostMapping("/search-associations")
    public ApiResponse<IPage<CameraPlateAssociation>> searchAssociations(
            @Valid @RequestBody SearchCriteria criteria) {
        IPage<CameraPlateAssociation> associations = cameraPlateService.selectByCameraOrPlateName(
                criteria.getPageNum(),
                criteria.getPageSize(),
                criteria.getCameraDeviceName(),
                criteria.getPlateDeviceName());
        ApiResponse<IPage<CameraPlateAssociation>> response = new ApiResponse<>(true, "查询成功", associations);
        response.setLength((int) associations.getTotal());
        return response;
    }

    @PostMapping("/export-associations")
    public ResponseEntity<ByteArrayResource> exportAssociations(@Valid @RequestBody SearchCriteria criteria) throws IOException {
        // 获取所有匹配的记录，不进行分页
        List<CameraPlateAssociation> associations = cameraPlateService.selectAllByCameraOrPlateName(
                criteria.getCameraDeviceName(),
                criteria.getPlateDeviceName());

        // 创建工作簿和工作表
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("相机-板台关联管理");

        // 创建标题行
        Row headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("相机名");
        headerRow.createCell(1).setCellValue("板台名");
        headerRow.createCell(2).setCellValue("轨道");
        headerRow.createCell(3).setCellValue("描述");

        // 填充数据
        int rowNum = 1;
        for (CameraPlateAssociation association : associations) {
            Row row = sheet.createRow(rowNum++);
            row.createCell(0).setCellValue(association.getCameraDeviceName());
            row.createCell(1).setCellValue(association.getPlateDeviceName());
            row.createCell(2).setCellValue(association.getTrackNumber());
            row.createCell(3).setCellValue(association.getAssociationDescription());
        }

        // 将工作簿写入字节数组
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        workbook.close();

        // 创建ByteArrayResource
        ByteArrayResource resource = new ByteArrayResource(outputStream.toByteArray());

        // 设置HTTP头
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=camera_plate_associations.xlsx");

        // 返回ResponseEntity
        return ResponseEntity.ok()
                .headers(headers)
                .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                .body(resource);
    }


    @PostMapping("/import-camera-plate")
    public ApiResponse<Map<String, Boolean>> importCameraPlateAssociations(@RequestParam("file") MultipartFile file) {
        try {
            List<CameraPlateAssociation> associations = readExcelFile(file);
            Map<String, Boolean> results = cameraPlateService.upsertCameraPlateAssociations(associations);
            ApiResponse<Map<String, Boolean>> response = new ApiResponse<>(true, "导入成功", results);
            response.setLength(results.size());
            return response;
        } catch (Exception e) {
            return new ApiResponse<>(false, "导入数据错误: " , null);
        }
    }

    private List<CameraPlateAssociation> readExcelFile(MultipartFile file) throws IOException {
        List<CameraPlateAssociation> associations = new ArrayList<>();
        Workbook workbook = new XSSFWorkbook(file.getInputStream());
        Sheet sheet = workbook.getSheetAt(0);

        // 校验标题行
        Row headerRow = sheet.getRow(0);
        if (headerRow == null || !validateHeaderRow(headerRow)) {
            workbook.close();
            throw new IllegalArgumentException("Excel文件的标题行不符合预期");
        }

        for (Row row : sheet) {
            if (row.getRowNum() == 0) continue; // 跳过标题行

            CameraPlateAssociation association = new CameraPlateAssociation();
            association.setCameraDeviceName(getCellValueAsString(row.getCell(0)));
            association.setPlateDeviceName(getCellValueAsString(row.getCell(1)));
            association.setTrackNumber(getCellValueAsInteger(row.getCell(2)));
            association.setAssociationDescription(getCellValueAsString(row.getCell(3)));

            validateAssociation(association);
            associations.add(association);
        }

        workbook.close();
        return associations;
    }

    private boolean validateHeaderRow(Row headerRow) {
        String[] expectedHeaders = {
                "相机名", "板台名", "轨道", "描述"
        };

        for (int i = 0; i < expectedHeaders.length; i++) {
            Cell cell = headerRow.getCell(i);
            if (cell == null || !expectedHeaders[i].equals(cell.getStringCellValue())) {
                return false;
            }
        }
        return true;
    }

    private String getCellValueAsString(Cell cell) {
        if (cell == null) return null;
        switch (cell.getCellType()) {
            case STRING: return cell.getStringCellValue();
            case NUMERIC: return String.valueOf(cell.getNumericCellValue());
            default: return null;
        }
    }

    private Integer getCellValueAsInteger(Cell cell) {
        if (cell == null) return null;
        switch (cell.getCellType()) {
            case NUMERIC: return (int) cell.getNumericCellValue();
            case STRING: return Integer.parseInt(cell.getStringCellValue());
            default: return null;
        }
    }

    private void validateAssociation(CameraPlateAssociation association) {
        if (association.getCameraDeviceName() == null || association.getCameraDeviceName().trim().isEmpty()) {
            throw new IllegalArgumentException("相机名 is required");
        }
        if (association.getPlateDeviceName() == null || association.getPlateDeviceName().trim().isEmpty()) {
            throw new IllegalArgumentException("Plate device name is required");
        }
        if (association.getTrackNumber() == null || (association.getTrackNumber() != 1 && association.getTrackNumber() != 2)) {
            throw new IllegalArgumentException("Track number must be 1 or 2");
        }
    }



    @PostMapping("/delete-camera-plate-associations")
    public ApiResponse<Map<String, Boolean>> deleteCameraPlateAssociations(
            @Valid @RequestBody DeleteCameraPlateRequest request) {
        Map<String, Boolean> results = cameraPlateService.deleteCameraPlateAssociations(request.getCameraDeviceNames());
        ApiResponse<Map<String, Boolean>> response = new ApiResponse<>(true, "删除操作完成", results);
        response.setLength(results.size());
        return response;
    }


    @PostMapping("/unbound-cameras")
    public ApiResponse<List<String>> getUnboundCameras(@Valid @RequestBody UnboundCameraRequest request) {
        List<String> cameras = cameraPlateService.getUnboundCameraNames(request.getCameraName());
        return new ApiResponse<>(true, "Successfully retrieved unbound cameras", cameras);
    }

    @PostMapping("/unbound-plates")
    public ApiResponse<List<String>> getUnboundPlates(@Valid @RequestBody UnboundPlateRequest request) {
        List<String> plates = cameraPlateService.getUnboundPlateNamesByTrackAndName(request.getTrackNumber(), request.getPlateName());
        return new ApiResponse<>(true, "Successfully retrieved unbound plates", plates);
    }

    @Data
    public static class UnboundCameraRequest {
        private String cameraName;
    }

    @Data
    public static class UnboundPlateRequest {
        @Min(value = 1, message = "Track number must be 1 or 2")
        @Max(value = 2, message = "Track number must be 1 or 2")
        @NotNull
        private Integer trackNumber;

        private String plateName;
    }
}
