/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.cachingmachine.controller;

import java.sql.Timestamp;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.hihonor.iot.plc.ApiResponse;
import com.hihonor.iot.plc.cachingmachine.controller.request.GetHistoryValuesRequest;
import com.hihonor.iot.plc.cachingmachine.controller.request.GetValuesRequest;
import com.hihonor.iot.plc.cachingmachine.controller.request.SendDataRequest;
import com.hihonor.iot.plc.cachingmachine.repo.PlateDevice;
import com.hihonor.iot.plc.cachingmachine.repo.PlateDeviceService;
import com.hihonor.iot.plc.thing.ThingInterface;
import com.hihonor.iot.plc.thing.ThingManagement;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
@RestController
//@RequestMapping("/plc")
@Slf4j
public class IotManuController {

    @Autowired
    ThingManagement thingManagement;
    @Autowired
    PlateDeviceService plateDeviceService;

    /**
     * 发送数据
     *
     * @param request request
     * @return ApiResponse
     */
  //  @PostMapping("/getValues")
    public ApiResponse<Map<String, String>> getValues(@RequestBody @Valid GetValuesRequest request) {
        String deviceName = request.getIotId();
        ThingInterface plc = (ThingInterface) thingManagement.getThing(deviceName);
        ApiResponse<Map<String, String>> response = new ApiResponse<>();
        if (plc == null) {
            response.setSuccess(false);
            response.setMessage("Device not found: " + deviceName);
            log.info("Device not found: {}", deviceName);
            response.setData(null);
        } else if (plc.getAttributes() == null) {
            response.setSuccess(false);
            response.setMessage(deviceName + ":Device not connect，No data can be obtained");
            log.info("Device not connect，No data can be obtained: {}", deviceName);
            response.setData(null);

        } else {
            Map<String, String> values = plc.getAttributes();
            response.setSuccess(true);
            response.setMessage("Data retrieved successfully");
            response.setData(values);
            response.setLength(1);
            log.info("Data retrieved successfully");
        }
        return response;
    }

    /**
     * 发送数据
     *
     * @param request request
     * @return ApiResponse
     */
  //  @PostMapping("/getOnlineStatus")
    public ApiResponse<Map<String, String>> getOnlineStatus(@RequestBody @Valid GetValuesRequest request) {
        String deviceName = request.getIotId();
        ThingInterface plc = (ThingInterface) thingManagement.getThing(deviceName);
        ApiResponse<Map<String, String>> response = new ApiResponse<>();
        if (plc == null) {
            response.setSuccess(false);
            response.setMessage("Device not found: " + deviceName);
            log.info("Device not found: {}", deviceName);
            response.setData(null);
        } else {
            Map<String, String> values = new HashMap<String, String>();
            values.put("onlineStatus", String.valueOf(plc.getOnlineStatus()));
            response.setSuccess(true);
            response.setMessage("Data retrieved successfully");
            response.setData(values);
            response.setLength(1);
            log.info("Data retrieved successfully");
        }
        return response;
    }

    /**
     * findByDeviceIdAndTimestampRange
     *
     * @param request request
     * @return ApiResponse
     */
   /// @PostMapping("/getHistoryValues")
    public ApiResponse<List<PlateDevice>> findByDeviceIdAndTimestampRange(@RequestBody @Valid GetHistoryValuesRequest request) {
        String deviceId = request.getIotId();

        // 如果 startTimestamp 为空，则使用一个很早的时间点
        Timestamp startTimestamp = Optional.ofNullable(request.getStartTimestamp()).orElseGet(() -> {
            Calendar calendar = Calendar.getInstance();
            calendar.set(1970, Calendar.JANUARY, 1, 0, 0, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            return new Timestamp(calendar.getTimeInMillis());
        });

// 如果 endTimestamp 为空，则使用一个未来的时间点
        Timestamp endTimestamp = Optional.ofNullable(request.getEndTimestamp()).orElseGet(() -> {
            Calendar calendar = Calendar.getInstance();
            calendar.set(3000, Calendar.JANUARY, 1, 0, 0, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            return new Timestamp(calendar.getTimeInMillis());
        });
        List<PlateDevice> devices = plateDeviceService.findByDeviceIdAndTimestampRange(deviceId, startTimestamp, endTimestamp);
        ApiResponse<List<PlateDevice>> response = new ApiResponse<>();
        response.setSuccess(true);
        response.setMessage("Data retrieved successfully");
        response.setData(devices);
        response.setLength(devices.size());

        return response;
    }

    /**
     * 发送数据
     *
     * @param request request
     * @return ApiResponse
     */
   // @PostMapping("/sendData")
    public ApiResponse<Boolean> sendData(@RequestBody @Valid SendDataRequest request) {
        String deviceName = request.getIotId();
        List<Map<String, Object>> listOfMaps = request.getRows();

        ThingInterface thing = (ThingInterface) thingManagement.getThing(deviceName);
        ApiResponse<Boolean> response = new ApiResponse<>();
        if (thing == null) {
            response.setSuccess(false);
            response.setMessage("Device not found: " + deviceName);
            response.setData(null);
        } else {
            boolean result = thing.sendConfiguration(listOfMaps);
            response.setSuccess(result);
            response.setMessage(result ? "Data sent successfully" : "Data could not be sent");
            response.setData(result);
        }
        return response;
    }

    /**
     * restart
     *
     * @return ApiResponse
     */
   // @PostMapping("/restart")
  //  @ForwardToNodes
    public ApiResponse<Boolean> restart() {

        log.info(" controller restart");
        thingManagement.updateThing();
        ApiResponse<Boolean> response = new ApiResponse<>();
        response.setMessage("restart successfully");
        response.setData(true);
        response.setLength(0);
        response.setSuccess(true);
        return response;
    }
}
