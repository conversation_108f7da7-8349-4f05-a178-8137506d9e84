#logging:
#  config: logback-spring.xml
#spring:
#  application:
#    name: ideahub-agent-test
#  profiles:
#    active: test
#  cloud:
#    nacos:
#      config:
#        prefix: ideahub-config
#        server-addr: yun-dev.test.hihonor.com:80
#        username: IOT_Integration
#        password: m4GMmGqUn6KzNKb9MYKg
#        namespace: IOT_Integration
#        group: test
#        file-extension: yaml
#        # 共享公共配置
#        shared-configs[0]:
#          data-id: equipment-common-test.yaml
#          group: test
#          refresh: true
#
logging:
  config: classpath:logback-spring.xml
#spring:
#  application:
#    name: plc-agent
#  profiles:
#    active: prod
#  cloud:
#    nacos:
#      config:
#        server-addr: mse.yun.hihonor.com:80
#        context-path: /nacos/service

# 测试环境使用pekctxt
docker_region: ctyun-cn-north1
# 应用配置环境
docker_env: pro
# 应用信息
application:
  # IAM项目ID，应用ID
  appId: 51a5b1d937a249a7b817c70acf7b8b33
  # 部署单元
  subAppId: plc-cachingMachine-agent
# IAM项目信息
#iam:
#  enterprise: 99999999999999999999999999999999
#  project: 51a5b1d937a249a7b817c70acf7b8b33
#  endpoint: http://apig.heds.hihonor.com/api
#  account: 51a5b1d937a249a7b817c70acf7b8b33
#  secret: 4STmH/pAYJZ7U6i3Xci3tPMOc0bu2V/le1TH56zd

spring:
  application:
    name: plc-agent
  profiles:
    active: prod
  cloud:
    nacos:
      config:
        endpoint: mse.yun.hihonor.com:80
        context-path: /nacos-address/service/nacos
    compatibility-verifier:
      enabled: false
#        server-addr: mse.yun.hihonor.com:80
#        context-path: /nacos/service
truss:
  apimall:
    enterprise: 99999999999999999999999999999999
    ## 生产为：https://yun.hihonor.com
    endpoint: https://yun.hihonor.com
    ## apimall中注册的API集成账号
    project: 51a5b1d937a249a7b817c70acf7b8b33
    account: 51a5b1d937a249a7b817c70acf7b8b33
    secret: 4STmH/pAYJZ7U6i3Xci3tPMOc0bu2V/le1TH56zd


# 流量切换模块配置
trafficswitch:
  api:
    base-url: http://agw.yun.hihonor.com  # API基础地址
    upstream-id: 558385788531556352        # 要查询的上游服务ID (请根据实际情况修改)

iam:
  enterprise: 99999999999999999999999999999999
  project: 51a5b1d937a249a7b817c70acf7b8b33
  endpoint: http://apig.heds.hihonor.com/api
  account: 51a5b1d937a249a7b817c70acf7b8b33
  secret: 4STmH/pAYJZ7U6i3Xci3tPMOc0bu2V/le1TH56zd


apimall:
  consumer: true
  endpoint: https://yun.hihonor.com
  account: 51a5b1d937a249a7b817c70acf7b8b33
  secret: 4STmH/pAYJZ7U6i3Xci3tPMOc0bu2V/le1TH56zd

